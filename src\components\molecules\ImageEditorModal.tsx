import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import CropIcon from '@mui/icons-material/Crop';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import ModalHeader from '~/components/molecules/ModalHeader';
import { ImageEditorConfig } from '~/types/fileUpload';
import {
  canvasToFile,
  canvasToMultipleFiles,
  cleanupImageUrl,
  getAspectRatioGroups,
  getCropSessionCount,
  getEditingDimensions,
  getEditingDimensionsForGroup,
  isMultiAspectRatioConfig,
  loadImageFromFile,
} from '~/utils/imageEditorUtils';

interface ImageEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (files: File[]) => void;
  onCancel?: () => void;
  file: File | null;
  config: ImageEditorConfig;
  fileType?: string; // Add fileType for proper aspect ratio grouping
  uploading?: boolean; // New: Show upload progress
  batchInfo?: {
    current: number;
    total: number;
    // Enhanced for multi-file, multi-aspect-ratio workflow
    fileIndex?: number;
    totalFiles?: number;
    aspectRatioIndex?: number;
    totalAspectRatios?: number;
    aspectRatioName?: string;
  };
  // New: Multi-aspect-ratio support
  aspectRatioInfo?: {
    currentGroup: number;
    totalGroups: number;
    groupName: string;
  };
}

export const ImageEditorModal: React.FC<ImageEditorModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onCancel,
  file,
  config,
  fileType,
  uploading = false,
  batchInfo,
  aspectRatioInfo,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [imagePosition, setImagePosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [initialPinchDistance, setInitialPinchDistance] = useState<number>(0);
  const [initialZoom, setInitialZoom] = useState<number>(1);
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const wheelListenerRef = useRef<((e: WheelEvent) => void) | null>(null);

  // Create a callback ref that attaches the wheel listener immediately when the element is available
  const imageContainerCallbackRef = useCallback(
    (element: HTMLDivElement | null) => {
      // Clean up previous listener if it exists
      if (imageContainerRef.current && wheelListenerRef.current) {
        imageContainerRef.current.removeEventListener(
          'wheel',
          wheelListenerRef.current
        );
        wheelListenerRef.current = null;
      }

      // Update the ref
      imageContainerRef.current = element;

      // Attach new listener if element exists and conditions are met
      if (element && isOpen && !isMobile) {
        const handleWheel = (e: WheelEvent) => {
          e.preventDefault();
          const zoomStep = config?.zoomStep || 0.05; // 5% step
          const delta = e.deltaY > 0 ? -zoomStep : zoomStep;

          setZoom(prevZoom => {
            const newZoom = prevZoom + delta;
            const minZoom = 0.01; // 1% minimum zoom
            const maxZoom = 3;
            return Math.max(minZoom, Math.min(maxZoom, newZoom));
          });
        };

        wheelListenerRef.current = handleWheel;
        element.addEventListener('wheel', handleWheel, { passive: false });
      }
    },
    [isOpen, isMobile, config?.zoomStep]
  );

  // Multi-aspect-ratio state
  const aspectRatioGroups = useMemo(
    () => getAspectRatioGroups(config, fileType),
    [config, fileType]
  );
  const isMultiAspectRatio = useMemo(
    () => isMultiAspectRatioConfig(config, fileType),
    [config, fileType]
  );
  const currentGroup = useMemo(() => {
    return aspectRatioInfo
      ? aspectRatioGroups[aspectRatioInfo.currentGroup - 1]
      : aspectRatioGroups[0];
  }, [aspectRatioInfo, aspectRatioGroups]);

  // Get distance between two touch points
  const getTouchDistance = useCallback((touches: React.TouchList) => {
    if (touches.length < 2) return 0;
    const touch1 = touches[0];
    const touch2 = touches[1];
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  }, []);

  // Load image when file changes
  useEffect(() => {
    if (!file || !isOpen) {
      // Clean up previous image blob URL
      cleanupImageUrl(image);
      setImage(null);
      setImagePosition({ x: 0, y: 0 });
      setZoom(1); // Reset to default, will be recalculated when new image loads
      setError(null);
      // Don't reset loading if we're opening with uploading=true (parent is handling upload)
      if (!uploading) {
        setLoading(false);
      }
      return;
    }

    setLoading(true);
    setError(null);

    loadImageFromFile(file)
      .then(loadedImage => {
        // Clean up previous image blob URL before setting new one
        cleanupImageUrl(image);
        setImage(loadedImage);

        // Calculate and set optimal initial zoom inline to avoid dependency issues
        const editingDimensions = currentGroup
          ? getEditingDimensionsForGroup(currentGroup)
          : getEditingDimensions(config);
        const targetWidth = editingDimensions.width;
        const targetHeight = editingDimensions.height;

        const imageWidth = loadedImage.naturalWidth;
        const imageHeight = loadedImage.naturalHeight;

        let initialZoom = 1;
        // If image is smaller than target, zoom in to fill the crop area
        if (imageWidth <= targetWidth && imageHeight <= targetHeight) {
          const scaleX = targetWidth / imageWidth;
          const scaleY = targetHeight / imageHeight;
          initialZoom = Math.min(scaleX, scaleY);
        } else {
          // If image is larger than target, calculate zoom so that exactly targetWidth x targetHeight pixels
          // of the original image are visible within the crop area
          const maxDisplayWidth = isMobile ? 280 : 400;
          const maxDisplayHeight = isMobile ? 280 : 400;
          const displayWidth = Math.min(targetWidth, maxDisplayWidth);
          const displayHeight = Math.min(targetHeight, maxDisplayHeight);

          // Calculate how much the display is scaled down from the target
          const displayScale = displayWidth / targetWidth;

          // We want to show targetWidth pixels of the image within displayWidth pixels
          initialZoom = displayScale;
        }

        setZoom(initialZoom);

        // Center the image initially
        setImagePosition({ x: 0, y: 0 });

        // Reset tooltip state for new image
        setShowTooltip(false);
      })
      .catch(err => {
        console.error('Failed to load image:', err);
        setError('Failed to load image. Please try again.');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [file, isOpen, uploading, isMobile, config, currentGroup]);

  // Cleanup blob URL on component unmount
  useEffect(() => {
    return () => {
      cleanupImageUrl(image);
    };
  }, [image]);

  // Reset loading state when upload finishes or modal closes
  useEffect(() => {
    if (!uploading && !isOpen) {
      setLoading(false);
    }
  }, [uploading, isOpen]);

  // Close tooltip when clicking outside (mobile)
  useEffect(() => {
    if (!isMobile || !showTooltip) return;

    const handleClickOutside = (e: TouchEvent) => {
      // Don't close tooltip if the touch is on a button or interactive element
      const target = e.target as Element;
      if (
        target &&
        (target.closest('button') ||
          target.closest('[role="button"]') ||
          target.closest('.MuiButton-root') ||
          target.closest('.MuiIconButton-root'))
      ) {
        return;
      }
      setShowTooltip(false);
    };

    // Add a small delay to avoid immediate closing when opening
    const timer = setTimeout(() => {
      document.addEventListener('touchstart', handleClickOutside);
    }, 100);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isMobile, showTooltip]);

  // Update preview canvas when image position or zoom changes
  useEffect(() => {
    // Get the actual dimensions we'll be editing with for the current aspect ratio group
    const editingDimensions = currentGroup
      ? getEditingDimensionsForGroup(currentGroup)
      : getEditingDimensions(config);
    const targetWidth = editingDimensions.width;
    const targetHeight = editingDimensions.height;

    if (!image || !canvasRef.current || !targetWidth || !targetHeight) {
      return;
    }

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = targetWidth;
    canvas.height = targetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate the display scale factor for the crop overlay
    const maxDisplayWidth = isMobile ? 280 : 400;
    const maxDisplayHeight = isMobile ? 280 : 400;
    const displayWidth = Math.min(targetWidth, maxDisplayWidth);
    const displayHeight = Math.min(targetHeight, maxDisplayHeight);
    const scaleX = targetWidth / displayWidth;
    const scaleY = targetHeight / displayHeight;

    // Calculate image dimensions and position in the display
    const imageWidth = image.naturalWidth * zoom;
    const imageHeight = image.naturalHeight * zoom;

    // Calculate where the image appears relative to the crop area
    // Account for the scaling between display size and actual target size
    const imageX =
      (imagePosition.x - imageWidth / 2 + displayWidth / 2) * scaleX;
    const imageY =
      (imagePosition.y - imageHeight / 2 + displayHeight / 2) * scaleY;
    const scaledImageWidth = imageWidth * scaleX;
    const scaledImageHeight = imageHeight * scaleY;

    // Draw the image on the canvas exactly as it appears in the crop area
    ctx.drawImage(image, imageX, imageY, scaledImageWidth, scaledImageHeight);
  }, [image, imagePosition, zoom, config, currentGroup, isMobile]);

  // Handle zoom change
  const handleZoomChange = useCallback((newZoom: number) => {
    const minZoom = 0.01; // 1% minimum zoom
    const maxZoom = 3;
    const clampedZoom = Math.max(minZoom, Math.min(maxZoom, newZoom));
    setZoom(clampedZoom);
  }, []);

  // Handle zoom in
  const handleZoomIn = useCallback(() => {
    const zoomStep = 0.01; // 1% step for buttons
    setZoom(prevZoom => {
      const newZoom = prevZoom + zoomStep;
      const minZoom = 0.01; // 1% minimum zoom
      const maxZoom = 3;
      return Math.max(minZoom, Math.min(maxZoom, newZoom));
    });
  }, []);

  // Handle zoom out
  const handleZoomOut = useCallback(() => {
    const zoomStep = 0.01; // 1% step for buttons
    setZoom(prevZoom => {
      const newZoom = prevZoom - zoomStep;
      const minZoom = 0.01; // 1% minimum zoom
      const maxZoom = 3;
      return Math.max(minZoom, Math.min(maxZoom, newZoom));
    });
  }, []);

  // Handle mouse down for dragging
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y,
      });
    },
    [imagePosition]
  );

  // Handle mouse move for dragging
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDragging) return;

      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    },
    [isDragging, dragStart]
  );

  // Handle mouse up for dragging
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Touch event handlers for mobile
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      // Check if the touch target is a button or interactive element
      const target = e.target as Element;
      if (
        target &&
        (target.closest('button') ||
          target.closest('[role="button"]') ||
          target.closest('.MuiButton-root') ||
          target.closest('.MuiIconButton-root'))
      ) {
        // Don't handle touch events on buttons - let them work normally
        return;
      }

      // Don't stop propagation to allow normal event flow
      // e.stopPropagation();
      // Don't call preventDefault() - touchAction: 'none' handles this
      if (e.touches.length === 1) {
        // Single touch - start dragging
        const touch = e.touches[0];
        setIsDragging(true);
        setDragStart({
          x: touch.clientX - imagePosition.x,
          y: touch.clientY - imagePosition.y,
        });
      } else if (e.touches.length === 2) {
        // Two touches - start pinching
        setIsDragging(false);
        const distance = getTouchDistance(e.touches);
        setInitialPinchDistance(distance);
        setInitialZoom(zoom);
      }
    },
    [imagePosition, getTouchDistance, zoom]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      // Check if the touch target is a button or interactive element
      const target = e.target as Element;
      if (
        target &&
        (target.closest('button') ||
          target.closest('[role="button"]') ||
          target.closest('.MuiButton-root') ||
          target.closest('.MuiIconButton-root'))
      ) {
        // Don't handle touch events on buttons - let them work normally
        return;
      }

      // Don't stop propagation to allow normal event flow
      // e.stopPropagation();
      // Don't call preventDefault() - touchAction: 'none' handles this

      if (e.touches.length === 1 && isDragging) {
        // Single touch - dragging
        const touch = e.touches[0];
        setImagePosition({
          x: touch.clientX - dragStart.x,
          y: touch.clientY - dragStart.y,
        });
      } else if (e.touches.length === 2 && initialPinchDistance > 0) {
        // Two touches - pinching
        const currentDistance = getTouchDistance(e.touches);
        const scale = currentDistance / initialPinchDistance;
        const newZoom = initialZoom * scale;
        handleZoomChange(newZoom);
      }
    },
    [
      isDragging,
      dragStart,
      initialPinchDistance,
      initialZoom,
      getTouchDistance,
      handleZoomChange,
    ]
  );

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    // Check if the touch target is a button or interactive element
    const target = e.target as Element;
    if (
      target &&
      (target.closest('button') ||
        target.closest('[role="button"]') ||
        target.closest('.MuiButton-root') ||
        target.closest('.MuiIconButton-root'))
    ) {
      // Don't handle touch events on buttons - let them work normally
      return;
    }

    // Don't stop propagation to allow normal event flow
    // e.stopPropagation();
    setIsDragging(false);
    setInitialPinchDistance(0);
    setInitialZoom(1);
  }, []);

  // Handle save
  const handleSave = useCallback(async () => {
    console.log('🎯 handleSave called!', {
      hasImage: !!image,
      hasFile: !!file,
      hasCanvas: !!canvasRef.current,
      isMobile,
    });

    if (!image || !file || !canvasRef.current) {
      console.log('❌ handleSave early return - missing requirements');
      return;
    }

    console.log('✅ handleSave proceeding with save...');
    setLoading(true);
    setError(null);

    try {
      console.log('🎨 Starting image save process for:', file.name, {
        aspectRatioInfo,
        batchInfo,
        fileSize: file.size,
        fileType: file.type,
        currentGroup,
      });
      // Get the preview canvas
      const previewCanvas = canvasRef.current;
      const previewCtx = previewCanvas.getContext('2d');
      if (!previewCtx) {
        throw new Error('Could not get canvas context');
      }

      // Create a new canvas for the cropped output
      const outputCanvas = document.createElement('canvas');
      const outputCtx = outputCanvas.getContext('2d');
      if (!outputCtx) {
        throw new Error('Could not create output canvas context');
      }

      // Get current dimensions from config
      const dimensions = currentGroup
        ? getEditingDimensionsForGroup(currentGroup)
        : getEditingDimensions(config);
      if (!dimensions) {
        throw new Error('No dimensions found for current configuration');
      }

      // Set output canvas size to the target dimensions
      outputCanvas.width = dimensions.width;
      outputCanvas.height = dimensions.height;

      // Get the display area dimensions for cropping calculation
      const maxDisplayWidth = isMobile ? 280 : 400;
      const maxDisplayHeight = isMobile ? 280 : 400;
      const displayWidth = Math.min(dimensions.width, maxDisplayWidth);
      const displayHeight = Math.min(dimensions.height, maxDisplayHeight);

      // Calculate how the image is positioned and scaled in the display
      const imageDisplayWidth = image.naturalWidth * zoom;
      const imageDisplayHeight = image.naturalHeight * zoom;

      // Calculate the center of the display area
      const displayCenterX = displayWidth / 2;
      const displayCenterY = displayHeight / 2;

      // Calculate where the image center is positioned relative to display center
      const imageCenterX = displayCenterX + imagePosition.x;
      const imageCenterY = displayCenterY + imagePosition.y;

      // Calculate the top-left corner of the image as displayed
      const imageLeftX = imageCenterX - imageDisplayWidth / 2;
      const imageTopY = imageCenterY - imageDisplayHeight / 2;

      // The crop area is the visible part of the display (displayWidth x displayHeight)
      // Calculate what part of the displayed image this corresponds to
      const cropLeftInDisplay = 0; // Always crop from display area origin
      const cropTopInDisplay = 0;
      const cropWidthInDisplay = displayWidth;
      const cropHeightInDisplay = displayHeight;

      // Convert from display coordinates to image coordinates
      const scaleToOriginal = 1 / zoom; // Convert from display scale to original scale

      // Calculate crop bounds in original image coordinates
      const sourceX = (cropLeftInDisplay - imageLeftX) * scaleToOriginal;
      const sourceY = (cropTopInDisplay - imageTopY) * scaleToOriginal;
      const sourceWidth = cropWidthInDisplay * scaleToOriginal;
      const sourceHeight = cropHeightInDisplay * scaleToOriginal;

      // Determine output dimensions and scaling behavior
      let outputWidth, outputHeight;

      if (config.allowFreeformCrop) {
        // Freeform: use the actual source dimensions being cropped
        const clampedSourceX = Math.max(
          0,
          Math.min(sourceX, image.naturalWidth - 1)
        );
        const clampedSourceY = Math.max(
          0,
          Math.min(sourceY, image.naturalHeight - 1)
        );
        const clampedSourceWidth = Math.min(
          sourceWidth,
          image.naturalWidth - clampedSourceX
        );
        const clampedSourceHeight = Math.min(
          sourceHeight,
          image.naturalHeight - clampedSourceY
        );

        outputWidth = Math.round(Math.max(1, clampedSourceWidth));
        outputHeight = Math.round(Math.max(1, clampedSourceHeight));

        // Set output canvas size and draw the cropped area
        outputCanvas.width = outputWidth;
        outputCanvas.height = outputHeight;
        outputCtx.clearRect(0, 0, outputWidth, outputHeight);
        outputCtx.drawImage(
          image,
          clampedSourceX,
          clampedSourceY,
          clampedSourceWidth,
          clampedSourceHeight,
          0,
          0,
          outputWidth,
          outputHeight
        );
      } else {
        // Fixed dimensions: output exactly what's visible in the crop area
        // This creates a "screenshot" of what the user sees, preserving exact positioning
        // If image is zoomed out and positioned off-center, the output will show exactly that
        outputWidth = dimensions.width;
        outputHeight = dimensions.height;

        // Set output canvas size
        outputCanvas.width = outputWidth;
        outputCanvas.height = outputHeight;

        // Clear with transparent background
        outputCtx.clearRect(0, 0, outputWidth, outputHeight);

        // Calculate what portion of the image is visible in the display area
        // We need to map from display coordinates to output coordinates
        const scaleDisplayToOutput = outputWidth / displayWidth; // Scale factor from display to output

        // Calculate where the image appears in the output canvas
        const outputImageLeftX = imageLeftX * scaleDisplayToOutput;
        const outputImageTopY = imageTopY * scaleDisplayToOutput;
        const outputImageWidth = imageDisplayWidth * scaleDisplayToOutput;
        const outputImageHeight = imageDisplayHeight * scaleDisplayToOutput;

        // Only draw the parts of the image that are visible in the crop area (output canvas bounds)
        const visibleLeft = Math.max(0, outputImageLeftX);
        const visibleTop = Math.max(0, outputImageTopY);
        const visibleRight = Math.min(
          outputWidth,
          outputImageLeftX + outputImageWidth
        );
        const visibleBottom = Math.min(
          outputHeight,
          outputImageTopY + outputImageHeight
        );

        // Only proceed if there's a visible area
        if (visibleLeft < visibleRight && visibleTop < visibleBottom) {
          const visibleWidth = visibleRight - visibleLeft;
          const visibleHeight = visibleBottom - visibleTop;

          // Calculate corresponding source coordinates in the original image
          const sourceVisibleLeft =
            (visibleLeft - outputImageLeftX) / scaleDisplayToOutput / zoom;
          const sourceVisibleTop =
            (visibleTop - outputImageTopY) / scaleDisplayToOutput / zoom;
          const sourceVisibleWidth = visibleWidth / scaleDisplayToOutput / zoom;
          const sourceVisibleHeight =
            visibleHeight / scaleDisplayToOutput / zoom;

          // Clamp source coordinates to image bounds
          const clampedSourceLeft = Math.max(
            0,
            Math.min(sourceVisibleLeft, image.naturalWidth)
          );
          const clampedSourceTop = Math.max(
            0,
            Math.min(sourceVisibleTop, image.naturalHeight)
          );
          const clampedSourceRight = Math.max(
            clampedSourceLeft,
            Math.min(sourceVisibleLeft + sourceVisibleWidth, image.naturalWidth)
          );
          const clampedSourceBottom = Math.max(
            clampedSourceTop,
            Math.min(
              sourceVisibleTop + sourceVisibleHeight,
              image.naturalHeight
            )
          );

          const clampedSourceWidth = clampedSourceRight - clampedSourceLeft;
          const clampedSourceHeight = clampedSourceBottom - clampedSourceTop;

          // Calculate corresponding output coordinates
          const outputLeft =
            visibleLeft +
            (clampedSourceLeft - sourceVisibleLeft) *
              scaleDisplayToOutput *
              zoom;
          const outputTop =
            visibleTop +
            (clampedSourceTop - sourceVisibleTop) * scaleDisplayToOutput * zoom;
          const outputDrawWidth =
            clampedSourceWidth * scaleDisplayToOutput * zoom;
          const outputDrawHeight =
            clampedSourceHeight * scaleDisplayToOutput * zoom;

          // Draw the visible portion of the image
          outputCtx.drawImage(
            image,
            clampedSourceLeft,
            clampedSourceTop,
            clampedSourceWidth,
            clampedSourceHeight,
            outputLeft,
            outputTop,
            outputDrawWidth,
            outputDrawHeight
          );
        }
      }

      // Generate files for the current aspect ratio group using the cropped canvas
      console.log('📦 Generating files from canvas:', {
        canvasWidth: outputCanvas.width,
        canvasHeight: outputCanvas.height,
        hasCurrentGroup: !!currentGroup,
      });

      const editedFiles = currentGroup
        ? await canvasToMultipleFiles(outputCanvas, file, config, currentGroup)
        : await canvasToMultipleFiles(outputCanvas, file, config);

      console.log('✅ Generated files:', {
        editedFilesCount: editedFiles.length,
        fileNames: editedFiles.map(f => f.name),
      });

      if (editedFiles.length === 0) {
        throw new Error('No cropped variants were created successfully');
      }

      // IMPORTANT: Include the original file along with the cropped variants
      // The FileUploadManager expects both the original and cropped variants
      const allFiles = [file, ...editedFiles];

      // Check if this is the last step in the entire workflow
      // For multi-aspect-ratio workflows, this considers both files and aspect ratios
      const isLastStep = batchInfo && batchInfo.current === batchInfo.total;

      console.log('🔄 Save decision:', {
        isLastStep,
        batchInfo,
        totalFiles: allFiles.length,
        aspectRatioInfo,
      });

      if (isLastStep) {
        // Last step: Call onSave with original + all cropped images and close modal
        console.log('🏁 Last step - saving and closing');
        onSave(allFiles);

        // Small delay to prevent flicker and show success state
        setTimeout(() => {
          onClose();
        }, 100);
      } else {
        // Not last step: Call onSave to process current step but keep modal open
        console.log('➡️ Not last step - saving and continuing');
        onSave(allFiles);
        // Note: Don't close modal - parent should handle moving to next step
      }
    } catch (err) {
      console.error('❌ Failed to save image:', file.name, err);
      setError(
        `Failed to save image: ${err instanceof Error ? err.message : 'Unknown error'}`
      );
      setLoading(false); // Always reset loading on error
    }
    // Note: Don't reset loading in finally block as the parent component
    // should handle the loading state during upload process
  }, [
    image,
    file,
    config,
    currentGroup,
    onSave,
    onClose,
    batchInfo,
    zoom,
    imagePosition,
    isMobile,
  ]); // Handle cancel
  const handleCancel = useCallback(() => {
    // In multi-image batch scenarios, always cancel the entire process
    // when user clicks the close (X) button
    if (batchInfo && batchInfo.total > 1) {
      onClose(); // Cancel entire batch
    } else if (onCancel) {
      onCancel(); // Single image - use provided cancel handler
    } else {
      onClose(); // Fallback to close
    }
  }, [onCancel, onClose, batchInfo]);

  // Handle tooltip toggle for mobile
  const handleTooltipToggle = useCallback(() => {
    setShowTooltip(prev => !prev);
  }, []);

  // Calculate current crop dimensions for display
  const getCurrentDimensions = () => {
    if (!image) return null;

    // If we have aspect ratio info, show current group's sizes
    if (currentGroup) {
      const sizeList = currentGroup.targetSizes
        .map(size => {
          // On desktop, show only dimensions; on mobile, include names
          if (isMobile) {
            // Use translations for size names if available, otherwise fallback to size.name
            const translatedName = size.name
              ? t(`imageSizes.${size.name}`, size.name)
              : undefined;
            return translatedName
              ? `${translatedName} (${size.width}×${size.height})`
              : `${size.width}×${size.height}`;
          } else {
            // Desktop: show only dimensions
            return `${size.width}×${size.height}`;
          }
        })
        .join(', ');
      return {
        current: sizeList,
        target: sizeList,
        isMultiple: currentGroup.targetSizes.length > 1,
        aspectRatioName: currentGroup.name,
      };
    }

    // Fallback to all target sizes if no specific group
    if (config.targetSizes && config.targetSizes.length > 0) {
      const sizeList = config.targetSizes
        .map(size => {
          // On desktop, show only dimensions; on mobile, include names
          if (isMobile) {
            // Use translations for size names if  available, otherwise fallback to size.name
            const translatedName = size.name
              ? t(`imageSizes.${size.name}`, size.name)
              : undefined;
            return translatedName
              ? `${translatedName} (${size.width}×${size.height})`
              : `${size.width}×${size.height}`;
          } else {
            // Desktop: show only dimensions
            return `${size.width}×${size.height}`;
          }
        })
        .join(', ');
      return {
        current: sizeList,
        target: sizeList,
        isMultiple: true,
      };
    }

    // Single target size
    const editingDimensions = getEditingDimensions(config);
    return {
      current: `${editingDimensions.width} × ${editingDimensions.height}`,
      target: `${editingDimensions.width} × ${editingDimensions.height}`,
      isMultiple: false,
    };
  };

  // Generate modal title with enhanced batch info
  const getModalTitle = () => {
    // Helper function to format aspect ratio
    const formatAspectRatio = (aspectRatio: number) => {
      if (Math.abs(aspectRatio - 1) < 0.01) {
        return '1:1';
      } else if (Math.abs(aspectRatio - 16 / 9) < 0.01) {
        return '16:9';
      } else if (Math.abs(aspectRatio - 3) < 0.01) {
        return '3:1';
      } else if (Math.abs(aspectRatio - 3.75) < 0.01) {
        return '3.75:1';
      } else if (Math.abs(aspectRatio - 2 / 3) < 0.01) {
        return '2:3';
      } else if (aspectRatio > 1) {
        // Use higher precision for better accuracy
        return `${aspectRatio.toFixed(2)}:1`.replace(/\.?0+:/, ':');
      } else {
        return `1:${(1 / aspectRatio).toFixed(2)}`.replace(/\.?0+$/, '');
      }
    };

    // Get current aspect ratio information
    let aspectRatioDisplay = '';
    if (currentGroup?.aspectRatio) {
      aspectRatioDisplay = formatAspectRatio(currentGroup.aspectRatio);
    } else if (batchInfo?.aspectRatioName) {
      // Extract aspect ratio from aspect ratio name if possible
      const ratioMatch = batchInfo.aspectRatioName.match(
        /(\d+(?:\.\d+)?):(\d+(?:\.\d+)?)/
      );
      if (ratioMatch) {
        aspectRatioDisplay = `${ratioMatch[1]}:${ratioMatch[2]}`;
      } else {
        aspectRatioDisplay = batchInfo.aspectRatioName;
      }
    }

    // Determine if we have multiple files
    const hasMultipleFiles =
      (batchInfo?.totalFiles && batchInfo.totalFiles > 1) || false;

    // Determine if we have multiple aspect ratios
    const hasMultipleAspectRatios =
      (batchInfo?.totalAspectRatios && batchInfo.totalAspectRatios > 1) ||
      (aspectRatioGroups && aspectRatioGroups.length > 1) ||
      false;

    // Get current indices
    const currentAspectRatioIndex =
      batchInfo?.aspectRatioIndex ||
      (currentGroup
        ? (aspectRatioGroups?.findIndex(
            (group: any) => group === currentGroup
          ) ?? 0) + 1
        : 1);
    const totalAspectRatios =
      batchInfo?.totalAspectRatios || aspectRatioGroups?.length || 1;
    const currentFileIndex = batchInfo?.fileIndex || 1;
    const totalFiles = batchInfo?.totalFiles || 1;

    // Build title based on requirements
    if (isMobile) {
      // Mobile view
      if (hasMultipleFiles) {
        if (hasMultipleAspectRatios) {
          return `Edit ${currentAspectRatioIndex} of ${totalAspectRatios} for Image ${currentFileIndex} of ${totalFiles}`;
        } else {
          return `Edit for Image ${currentFileIndex} of ${totalFiles}`;
        }
      } else {
        if (hasMultipleAspectRatios) {
          return `Edit ${currentAspectRatioIndex} of ${totalAspectRatios}`;
        } else {
          return 'Edit Image';
        }
      }
    } else {
      // Desktop view
      if (hasMultipleFiles) {
        if (hasMultipleAspectRatios) {
          return `Edit ${currentAspectRatioIndex} of ${totalAspectRatios} (Aspect Ratio ${aspectRatioDisplay}) for Image ${currentFileIndex} of ${totalFiles}`;
        } else {
          return `Edit (Aspect Ratio ${aspectRatioDisplay}) for Image ${currentFileIndex} of ${totalFiles}`;
        }
      } else {
        if (hasMultipleAspectRatios) {
          return `Edit ${currentAspectRatioIndex} of ${totalAspectRatios} (Aspect Ratio ${aspectRatioDisplay})`;
        } else {
          return `Edit (Aspect Ratio ${aspectRatioDisplay})`;
        }
      }
    }
  };

  // Generate save button text based on workflow progress
  const getSaveButtonText = () => {
    // Remove loading text since processing is too fast to show loading state
    // if (loading) {
    //   // Show different text for multiple sizes vs single size
    //   if (currentGroup && currentGroup.targetSizes.length > 1) {
    //     return 'Generating sizes...';
    //   }
    //   return 'Processing...';
    // }

    // Enhanced multi-file, multi-aspect-ratio workflow
    if (batchInfo?.totalFiles && batchInfo?.totalAspectRatios) {
      const isLastFile = batchInfo.fileIndex === batchInfo.totalFiles;
      const isLastAspectRatio =
        batchInfo.aspectRatioIndex === batchInfo.totalAspectRatios;

      if (isLastFile && isLastAspectRatio) {
        return 'Save All';
      } else if (isLastAspectRatio) {
        return 'Next Image';
      } else {
        return 'Next Edit';
      }
    }

    // Fallback to legacy multi-aspect-ratio flow
    if (aspectRatioInfo) {
      const isLastGroup =
        aspectRatioInfo.currentGroup === aspectRatioInfo.totalGroups;

      // If there's only one aspect ratio group, treat it as single aspect ratio
      if (aspectRatioInfo.totalGroups === 1) {
        if (batchInfo && batchInfo.total > 1) {
          const isLastImage = batchInfo.current === batchInfo.total;
          return isLastImage ? 'Save' : 'Next';
        }
        return 'Save';
      }

      if (batchInfo && batchInfo.total > 1) {
        const isLastImage = batchInfo.current === batchInfo.total;
        if (isLastGroup && isLastImage) return 'Save All';
        if (isLastGroup) return 'Next Image';
        return 'Next Edit';
      }
      return isLastGroup ? 'Save' : 'Next Edit';
    }

    // Single aspect ratio flow
    if (batchInfo && batchInfo.total > 1) {
      const isLastImage = batchInfo.current === batchInfo.total;
      return isLastImage ? 'Save' : 'Next';
    }

    return 'Save';
  };

  const dimensions = getCurrentDimensions();
  const editingDimensions = useMemo(() => {
    return currentGroup
      ? getEditingDimensionsForGroup(currentGroup)
      : getEditingDimensions(config);
  }, [currentGroup, config]);

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      fullWidth
      maxWidth={isMobile ? false : 'lg'}
      fullScreen={isMobile}
      sx={{
        '& .MuiDialog-paper': {
          height: isMobile ? '100vh' : '90vh',
          maxHeight: isMobile ? 'none' : '800px',
          margin: isMobile ? 0 : undefined,
        },
      }}
    >
      <ModalHeader handleClose={handleCancel} title={getModalTitle()}>
        <Button
          onClick={() => {
            console.log('🔘 Next Edit button clicked!', {
              loading,
              uploading,
              hasImage: !!image,
              disabled: !image,
              isMobile,
              buttonText: getSaveButtonText(),
            });
            handleSave();
          }}
          onTouchStart={e => {
            console.log('👆 Next Edit button touched!');
            // Ensure touch events work properly
            e.stopPropagation();
          }}
          disabled={!image}
          variant="contained"
          sx={{
            // Ensure button is clickable on mobile
            touchAction: 'manipulation',
            userSelect: 'none',
            // Ensure button is above other elements
            position: 'relative',
            zIndex: 1000,
          }}
        >
          {getSaveButtonText()}
        </Button>
      </ModalHeader>

      <DialogContent sx={{ p: 0, overflow: 'hidden', height: '100%' }}>
        {error && (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            height="100%"
            flexDirection="column"
            gap={2}
          >
            <Typography color="error">{error}</Typography>
            <Button onClick={handleCancel} variant="outlined">
              Close
            </Button>
          </Box>
        )}

        {/* Show loading state for initial image load only */}
        {loading && !image && !error && (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            height="100%"
          >
            <Typography>Loading image...</Typography>
          </Box>
        )}

        {/* Show main content when image is loaded, even during processing/uploading */}
        {!error && image && (
          <Box
            sx={{
              display: 'flex',
              height: '100%',
              flexDirection: isMobile ? 'column' : 'row',
              position: 'relative', // Add relative positioning for overlay
            }}
          >
            {/* Processing/Upload overlay */}
            {(loading || uploading) && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10,
                  backdropFilter: 'blur(2px)',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 2,
                    p: 3,
                    backgroundColor: 'white',
                    borderRadius: 2,
                    boxShadow: 3,
                  }}
                >
                  <CircularProgress size={40} />
                  <Typography variant="h6" color="primary">
                    {uploading
                      ? 'Uploading image variants...'
                      : 'Processing image...'}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    textAlign="center"
                  >
                    {uploading
                      ? 'Please wait while your image is being uploaded'
                      : 'Generating cropped variants'}
                  </Typography>
                </Box>
              </Box>
            )}
            {/* Main editing area */}
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                p: isMobile ? 1 : 2,
                minHeight: isMobile ? 'auto' : '400px',
                position: 'relative',
              }}
            >
              {/* Image editing area */}
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'grey.100',
                  borderRadius: 1,
                  overflow: 'hidden',
                  minHeight: isMobile ? '60vh' : '400px',
                  position: 'relative',
                  cursor: isDragging ? 'grabbing' : 'grab',
                  touchAction: 'none', // Prevent default touch behaviors
                }}
                ref={imageContainerCallbackRef}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                {/* Background image that user can move */}
                <img
                  src={image.src}
                  alt="Editable image"
                  style={{
                    position: 'absolute',
                    width: `${image.naturalWidth * zoom}px`,
                    height: `${image.naturalHeight * zoom}px`,
                    left: `calc(50% + ${imagePosition.x}px - ${(image.naturalWidth * zoom) / 2}px)`,
                    top: `calc(50% + ${imagePosition.y}px - ${(image.naturalHeight * zoom) / 2}px)`,
                    userSelect: 'none',
                    pointerEvents: 'none',
                  }}
                />

                {/* Fixed crop overlay */}
                {editingDimensions && (
                  <Box
                    sx={{
                      position: 'absolute',
                      width: isMobile
                        ? `${Math.min(editingDimensions.width, 280)}px`
                        : `${Math.min(editingDimensions.width, 400)}px`,
                      height: isMobile
                        ? `${Math.min(editingDimensions.height, 280)}px`
                        : `${Math.min(editingDimensions.height, 400)}px`,
                      border: '3px solid',
                      borderColor: 'primary.main',
                      backgroundColor: 'transparent',
                      boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.3)',
                      pointerEvents: 'none',
                      zIndex: 1,
                    }}
                  />
                )}

                {/* Center guides */}
                <Box
                  sx={{
                    position: 'absolute',
                    width: '1px',
                    height: '20px',
                    backgroundColor: 'primary.main',
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    pointerEvents: 'none',
                    zIndex: 2,
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    width: '20px',
                    height: '1px',
                    backgroundColor: 'primary.main',
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    pointerEvents: 'none',
                    zIndex: 2,
                  }}
                />

                {/* Mobile info icon in bottom right corner */}
                {isMobile && editingDimensions && (
                  <Tooltip
                    title={
                      dimensions?.isMultiple
                        ? `Crop to multiple sizes: ${dimensions.current}. Drag the image to reposition it and use pinch-to-zoom or the buttons below to scale it. The area within the border shows the largest size for editing.`
                        : `Crop to ${editingDimensions.width} × ${editingDimensions.height} pixels. Drag the image to reposition it and use pinch-to-zoom or the buttons below to scale it. The area within the border is what will be saved.`
                    }
                    placement="top-start"
                    arrow
                    open={showTooltip}
                    disableHoverListener
                    disableFocusListener
                    disableTouchListener
                  >
                    <IconButton
                      size="large"
                      onClick={handleTooltipToggle}
                      sx={{
                        position: 'absolute',
                        bottom: 16,
                        right: 16,
                        bgcolor: 'background.paper',
                        width: 48,
                        height: 48,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                        '&:hover': {
                          bgcolor: 'background.paper',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                        },
                        zIndex: 3,
                      }}
                    >
                      <InfoOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>

              {/* Mobile zoom controls */}
              {isMobile && config?.allowZoom !== false && (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 3,
                    p: 2,
                    bgcolor: 'background.paper',
                    borderTop: '1px solid',
                    borderColor: 'grey.200',
                    mt: 1,
                  }}
                >
                  <IconButton
                    onClick={handleZoomOut}
                    disabled={zoom <= 0.01}
                    size="large"
                    sx={{
                      bgcolor: 'grey.100',
                      width: 48,
                      height: 48,
                      '&:hover': { bgcolor: 'grey.200' },
                      '&:disabled': { bgcolor: 'grey.50' },
                    }}
                  >
                    <ZoomOutIcon />
                  </IconButton>

                  <Typography
                    variant="body1"
                    sx={{
                      minWidth: '80px',
                      textAlign: 'center',
                      fontWeight: 'medium',
                      fontSize: '1.1rem',
                    }}
                  >
                    {Math.round(zoom * 100)}%
                  </Typography>

                  <IconButton
                    onClick={handleZoomIn}
                    disabled={zoom >= 3}
                    size="large"
                    sx={{
                      bgcolor: 'grey.100',
                      width: 48,
                      height: 48,
                      '&:hover': { bgcolor: 'grey.200' },
                      '&:disabled': { bgcolor: 'grey.50' },
                    }}
                  >
                    <ZoomInIcon />
                  </IconButton>
                </Box>
              )}
            </Box>

            {/* Hidden canvas for mobile - needed for image processing */}
            {isMobile && (
              <canvas
                ref={canvasRef}
                style={{
                  position: 'absolute',
                  top: -9999,
                  left: -9999,
                  visibility: 'hidden',
                  pointerEvents: 'none',
                }}
              />
            )}

            {/* Side panel with preview and controls - Desktop only */}
            {!isMobile && (
              <Box
                sx={{
                  width: '300px',
                  borderLeft: '1px solid',
                  borderColor: 'grey.200',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: 'grey.50',
                }}
              >
                {/* Preview section */}
                <Box
                  sx={{
                    p: 2,
                    borderBottom: '1px solid',
                    borderColor: 'grey.200',
                  }}
                >
                  <Typography variant="subtitle1" gutterBottom>
                    {dimensions?.isMultiple
                      ? 'Preview (Largest Size)'
                      : 'Preview'}
                  </Typography>
                  {editingDimensions && (
                    <Box
                      sx={{
                        width:
                          editingDimensions.width > editingDimensions.height
                            ? '220px'
                            : 'auto',
                        height:
                          editingDimensions.height > editingDimensions.width
                            ? '220px'
                            : 'auto',
                        maxWidth: '220px',
                        maxHeight: '220px',
                        aspectRatio: `${editingDimensions.width} / ${editingDimensions.height}`,
                        border: '2px solid',
                        borderColor: 'primary.main',
                        borderRadius: 1,
                        overflow: 'hidden',
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      <canvas
                        ref={canvasRef}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                      />
                    </Box>
                  )}
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    textAlign="center"
                    display="block"
                  >
                    This is how your final image will look
                  </Typography>
                </Box>

                {/* Desktop Controls */}
                <Box sx={{ p: 2, flex: 1 }}>
                  {/* Zoom Control */}
                  {config?.allowZoom !== false && (
                    <Box sx={{ mb: 3 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mb: 1,
                        }}
                      >
                        <Typography variant="subtitle2">Zoom</Typography>
                        {config?.targetWidth && config?.targetHeight && (
                          <Tooltip
                            title={
                              dimensions?.isMultiple
                                ? `Target sizes: ${dimensions.current}. Drag the image to reposition it and use zoom to scale it. What you see in the preview shows the largest size for the best editing experience.`
                                : `Target size: ${editingDimensions.width} × ${editingDimensions.height} pixels. Drag the image to reposition it and use zoom to scale it. What you see in the preview is what you'll get.`
                            }
                            placement="right"
                            arrow
                          >
                            <IconButton size="small" sx={{ p: 0.5 }}>
                              <InfoOutlinedIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mb: 1,
                        }}
                      >
                        <IconButton
                          onClick={handleZoomOut}
                          disabled={zoom <= 0.01}
                          size="small"
                          sx={{ p: 1 }}
                        >
                          <ZoomOutIcon fontSize="small" />
                        </IconButton>

                        <Typography
                          variant="body2"
                          sx={{
                            flex: 1,
                            textAlign: 'center',
                            fontWeight: 'medium',
                          }}
                        >
                          {Math.round(zoom * 100)}%
                        </Typography>

                        <IconButton
                          onClick={handleZoomIn}
                          disabled={zoom >= 3}
                          size="small"
                          sx={{ p: 1 }}
                        >
                          <ZoomInIcon fontSize="small" />
                        </IconButton>
                      </Box>

                      <Typography variant="caption" color="text.secondary">
                        Use scroll wheel or buttons to zoom
                      </Typography>
                    </Box>
                  )}

                  {/* Instructions */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      How to Edit
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 1 }}
                    >
                      • Drag the image to reposition it
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 1 }}
                    >
                      • Use zoom buttons or scroll wheel
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • The blue box shows your final crop area
                    </Typography>
                  </Box>

                  {/* Current Dimensions */}
                  {dimensions && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        {dimensions.isMultiple ? 'Output Sizes' : 'Output Size'}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          wordBreak: dimensions.isMultiple
                            ? 'break-word'
                            : 'normal',
                          lineHeight: dimensions.isMultiple ? 1.4 : 'normal',
                        }}
                      >
                        {dimensions.current}{' '}
                        {dimensions.isMultiple ? '' : 'pixels'}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ImageEditorModal;
